<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerC0x8pLw\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerC0x8pLw/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerC0x8pLw.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerC0x8pLw\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerC0x8pLw\App_KernelDevDebugContainer([
    'container.build_hash' => 'C0x8pLw',
    'container.build_id' => '4295c0a5',
    'container.build_time' => 1752420790,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerC0x8pLw');
