<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_CLe7xTyService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.CLe7xTy' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.CLe7xTy'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', 'App\\Repository\\CourseRepository', 'getCourseRepositoryService', true],
            'enrollmentService' => ['privates', 'App\\Service\\CourseEnrollmentService', 'getCourseEnrollmentServiceService', true],
        ], [
            'courseRepository' => 'App\\Repository\\CourseRepository',
            'enrollmentService' => 'App\\Service\\CourseEnrollmentService',
        ]);
    }
}
