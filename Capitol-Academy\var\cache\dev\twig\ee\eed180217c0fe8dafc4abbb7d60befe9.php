<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/instructor/edit.html.twig */
class __TwigTemplate_239a116a595dffe1bf0559f405a89c5c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/edit.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/instructor/edit.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Edit Instructor - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Edit Instructor";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Edit Instructor</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">
    <!-- Flash Messages -->
    ";
        // line 16
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 16, $this->source); })()), "flashes", ["success"], "method", false, false, false, 16));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 17
            yield "        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>";
            // line 18
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 22
        yield "
    ";
        // line 23
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 23, $this->source); })()), "flashes", ["error"], "method", false, false, false, 23));
        foreach ($context['_seq'] as $context["_key"] => $context["message"]) {
            // line 24
            yield "        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>";
            // line 25
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["message"], "html", null, true);
            yield "
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['message'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 29
        yield "
    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Instructor: ";
        // line 37
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 37, $this->source); })()), "name", [], "any", false, false, false, 37), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"";
        // line 43
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
                    ";
        // line 57
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 57, $this->source); })()), 'form_start', ["attr" => ["enctype" => "multipart/form-data", "class" => "needs-validation", "novalidate" => true]]);
        yield "
                    
                    <div class=\"row\">
                        <!-- Left Column -->
                        <div class=\"col-md-8\">
                            <!-- Basic Information -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-user me-2\"></i>Basic Information
                                </h6>
                                
                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 70
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 70, $this->source); })()), "name", [], "any", false, false, false, 70), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 73
        yield "
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 76
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 76, $this->source); })()), "specialization", [], "any", false, false, false, 76), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 79
        yield "
                                    </div>
                                </div>

                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 85
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 85, $this->source); })()), "email", [], "any", false, false, false, 85), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 88
        yield "
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        ";
        // line 91
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 91, $this->source); })()), "phone", [], "any", false, false, false, 91), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 94
        yield "
                                    </div>
                                </div>

                                <div class=\"mb-3\">
                                    ";
        // line 99
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 99, $this->source); })()), "linkedinUrl", [], "any", false, false, false, 99), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 102
        yield "
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-file-text me-2\"></i>Biography
                                </h6>
                                ";
        // line 111
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 111, $this->source); })()), "bio", [], "any", false, false, false, 111), 'row', ["attr" => ["class" => "form-control", "rows" => 4], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 114
        yield "
                            </div>



                            <!-- Qualifications -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>Qualifications
                                </h6>
                                <div id=\"qualifications-container\">
                                    <div class=\"collection-items\">
                                        ";
        // line 126
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 126, $this->source); })()), "qualifications", [], "any", false, false, false, 126));
        foreach ($context['_seq'] as $context["_key"] => $context["qualification"]) {
            // line 127
            yield "                                            <div class=\"collection-item mb-2\">
                                                <div class=\"input-group\">
                                                    ";
            // line 129
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock($context["qualification"], 'widget', ["attr" => ["class" => "form-control"]]);
            yield "
                                                    <button type=\"button\" class=\"btn btn-outline-danger remove-collection-item\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['qualification'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 136
        yield "                                    </div>
                                    <button type=\"button\" class=\"btn btn-outline-primary add-collection-item\" 
                                            data-prototype=\"";
        // line 138
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 138, $this->source); })()), "qualifications", [], "any", false, false, false, 138), "vars", [], "any", false, false, false, 138), "prototype", [], "any", false, false, false, 138), 'widget'), "html_attr");
        yield "\">
                                        <i class=\"fas fa-plus me-2\"></i>Add Qualification
                                    </button>
                                </div>
                            </div>

                            <!-- Achievements -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-trophy me-2\"></i>Achievements
                                </h6>
                                <div id=\"achievements-container\">
                                    <div class=\"collection-items\">
                                        ";
        // line 151
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 151, $this->source); })()), "achievements", [], "any", false, false, false, 151));
        foreach ($context['_seq'] as $context["_key"] => $context["achievement"]) {
            // line 152
            yield "                                            <div class=\"collection-item mb-2\">
                                                <div class=\"input-group\">
                                                    ";
            // line 154
            yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock($context["achievement"], 'widget', ["attr" => ["class" => "form-control"]]);
            yield "
                                                    <button type=\"button\" class=\"btn btn-outline-danger remove-collection-item\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['achievement'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 161
        yield "                                    </div>
                                    <button type=\"button\" class=\"btn btn-outline-primary add-collection-item\" 
                                            data-prototype=\"";
        // line 163
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 163, $this->source); })()), "achievements", [], "any", false, false, false, 163), "vars", [], "any", false, false, false, 163), "prototype", [], "any", false, false, false, 163), 'widget'), "html_attr");
        yield "\">
                                        <i class=\"fas fa-plus me-2\"></i>Add Achievement
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class=\"col-md-4\">
                            <!-- Profile Image -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-image me-2\"></i>Profile Image
                                </h6>
                                
                                ";
        // line 178
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 178, $this->source); })()), "profileImage", [], "any", false, false, false, 178)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 179
            yield "                                    <div class=\"text-center mb-3\">
                                        <img src=\"";
            // line 180
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/instructors/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 180, $this->source); })()), "profileImage", [], "any", false, false, false, 180))), "html", null, true);
            yield "\" 
                                             alt=\"";
            // line 181
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 181, $this->source); })()), "name", [], "any", false, false, false, 181), "html", null, true);
            yield "\" 
                                             class=\"img-fluid rounded-circle\" 
                                             style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                ";
        }
        // line 186
        yield "                                
                                ";
        // line 187
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 187, $this->source); })()), "profileImageFile", [], "any", false, false, false, 187), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 190
        yield "
                                <small class=\"form-text text-muted\">
                                    Upload a new image to replace the current one. Recommended size: 300x300px.
                                </small>
                            </div>

                            <!-- Settings -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-cog me-2\"></i>Settings
                                </h6>
                                
                                <div class=\"mb-3\">
                                    ";
        // line 203
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 203, $this->source); })()), "displayOrder", [], "any", false, false, false, 203), 'row', ["attr" => ["class" => "form-control"], "label_attr" => ["class" => "form-label fw-bold"]]);
        // line 206
        yield "
                                </div>
                                
                                <div class=\"form-check\">
                                    ";
        // line 210
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 210, $this->source); })()), "isActive", [], "any", false, false, false, 210), 'widget', ["attr" => ["class" => "form-check-input"]]);
        yield "
                                    ";
        // line 211
        yield $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->searchAndRenderBlock(CoreExtension::getAttribute($this->env, $this->source, (isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 211, $this->source); })()), "isActive", [], "any", false, false, false, 211), 'label', ["label_attr" => ["class" => "form-check-label"]]);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

            <!-- Form Footer -->
            <div class=\"card-footer bg-light border-top-0\" style=\"border-radius: 0 0 15px 15px; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"";
        // line 220
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_index");
        yield "\" class=\"btn btn-outline-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <a href=\"";
        // line 224
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_instructor_show", ["emailPrefix" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["instructor"]) || array_key_exists("instructor", $context) ? $context["instructor"] : (function () { throw new RuntimeError('Variable "instructor" does not exist.', 224, $this->source); })()), "emailPrefix", [], "any", false, false, false, 224)]), "html", null, true);
        yield "\" class=\"btn btn-outline-info me-2\">
                            <i class=\"fas fa-eye me-2\"></i>View Details
                        </a>
                        <button type=\"submit\" class=\"btn\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 0.75rem 2rem; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\" onmouseover=\"this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.4)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(40, 167, 69, 0.3)';\">
                            <i class=\"fas fa-save me-2\"></i>Update Instructor
                        </button>
                    </div>
                </div>
            </div>

            ";
        // line 234
        yield         $this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderBlock((isset($context["form"]) || array_key_exists("form", $context) ? $context["form"] : (function () { throw new RuntimeError('Variable "form" does not exist.', 234, $this->source); })()), 'form_end');
        yield "
        </div>
    </div>
</div>

<style>
/* Collection form styling */
.collection-item {
    position: relative;
}

.collection-item .input-group {
    margin-bottom: 0.5rem;
}

.add-collection-item {
    border-style: dashed;
    border-width: 2px;
}

.add-collection-item:hover {
    border-style: solid;
}

/* Form styling */
.form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-control:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-color: #1e3c72;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a3461 0%, #245085 100%);
    border-color: #1a3461;
}
</style>

<script>
// Collection form handling for specializations, qualifications, and achievements
document.addEventListener('DOMContentLoaded', function() {
    // Add collection item functionality
    function addCollectionItem(container, prototype) {
        const index = container.children.length;
        const newItem = prototype.replace(/__name__/g, index);
        container.insertAdjacentHTML('beforeend', newItem);
    }

    // Remove collection item functionality
    function removeCollectionItem(button) {
        button.closest('.collection-item').remove();
    }

    // Initialize collection forms
    const collections = ['qualifications', 'achievements'];
    collections.forEach(function(collectionName) {
        const container = document.querySelector(`#\${collectionName}-container`);
        if (container) {
            // Add \"Add\" button functionality
            const addButton = container.querySelector('.add-collection-item');
            if (addButton) {
                addButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const prototype = this.dataset.prototype;
                    addCollectionItem(container.querySelector('.collection-items'), prototype);
                });
            }

            // Add \"Remove\" button functionality
            container.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-collection-item') || 
                    e.target.closest('.remove-collection-item')) {
                    e.preventDefault();
                    const button = e.target.classList.contains('remove-collection-item') ? 
                                  e.target : e.target.closest('.remove-collection-item');
                    removeCollectionItem(button);
                }
            });
        }
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    }
});
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/instructor/edit.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  477 => 234,  464 => 224,  457 => 220,  445 => 211,  441 => 210,  435 => 206,  433 => 203,  418 => 190,  416 => 187,  413 => 186,  405 => 181,  401 => 180,  398 => 179,  396 => 178,  378 => 163,  374 => 161,  361 => 154,  357 => 152,  353 => 151,  337 => 138,  333 => 136,  320 => 129,  316 => 127,  312 => 126,  298 => 114,  296 => 111,  285 => 102,  283 => 99,  276 => 94,  274 => 91,  269 => 88,  267 => 85,  259 => 79,  257 => 76,  252 => 73,  250 => 70,  234 => 57,  217 => 43,  208 => 37,  198 => 29,  188 => 25,  185 => 24,  181 => 23,  178 => 22,  168 => 18,  165 => 17,  161 => 16,  157 => 14,  144 => 13,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Edit Instructor - Capitol Academy Admin{% endblock %}

{% block page_title %}Edit Instructor{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_instructor_index') }}\">Instructors</a></li>
<li class=\"breadcrumb-item active\">Edit Instructor</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class=\"alert alert-success alert-dismissible fade show\">
            <i class=\"fas fa-check-circle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class=\"alert alert-danger alert-dismissible fade show\">
            <i class=\"fas fa-exclamation-triangle me-2\"></i>{{ message }}
            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-user-edit mr-3\" style=\"font-size: 2rem;\"></i>
                        Edit Instructor: {{ instructor.name }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Back to Instructors Button -->
                        <a href=\"{{ path('admin_instructor_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Instructors
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body p-4\" style=\"background: white; border-radius: 0 0 15px 15px;\">
                    {{ form_start(form, {'attr': {'enctype': 'multipart/form-data', 'class': 'needs-validation', 'novalidate': true}}) }}
                    
                    <div class=\"row\">
                        <!-- Left Column -->
                        <div class=\"col-md-8\">
                            <!-- Basic Information -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-user me-2\"></i>Basic Information
                                </h6>
                                
                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_row(form.name, {
                                            'attr': {'class': 'form-control'},
                                            'label_attr': {'class': 'form-label fw-bold'}
                                        }) }}
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_row(form.specialization, {
                                            'attr': {'class': 'form-control'},
                                            'label_attr': {'class': 'form-label fw-bold'}
                                        }) }}
                                    </div>
                                </div>

                                <div class=\"row\">
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_row(form.email, {
                                            'attr': {'class': 'form-control'},
                                            'label_attr': {'class': 'form-label fw-bold'}
                                        }) }}
                                    </div>
                                    <div class=\"col-md-6 mb-3\">
                                        {{ form_row(form.phone, {
                                            'attr': {'class': 'form-control'},
                                            'label_attr': {'class': 'form-label fw-bold'}
                                        }) }}
                                    </div>
                                </div>

                                <div class=\"mb-3\">
                                    {{ form_row(form.linkedinUrl, {
                                        'attr': {'class': 'form-control'},
                                        'label_attr': {'class': 'form-label fw-bold'}
                                    }) }}
                                </div>
                            </div>

                            <!-- Bio -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-file-text me-2\"></i>Biography
                                </h6>
                                {{ form_row(form.bio, {
                                    'attr': {'class': 'form-control', 'rows': 4},
                                    'label_attr': {'class': 'form-label fw-bold'}
                                }) }}
                            </div>



                            <!-- Qualifications -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-graduation-cap me-2\"></i>Qualifications
                                </h6>
                                <div id=\"qualifications-container\">
                                    <div class=\"collection-items\">
                                        {% for qualification in form.qualifications %}
                                            <div class=\"collection-item mb-2\">
                                                <div class=\"input-group\">
                                                    {{ form_widget(qualification, {'attr': {'class': 'form-control'}}) }}
                                                    <button type=\"button\" class=\"btn btn-outline-danger remove-collection-item\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    <button type=\"button\" class=\"btn btn-outline-primary add-collection-item\" 
                                            data-prototype=\"{{ form_widget(form.qualifications.vars.prototype)|e('html_attr') }}\">
                                        <i class=\"fas fa-plus me-2\"></i>Add Qualification
                                    </button>
                                </div>
                            </div>

                            <!-- Achievements -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-trophy me-2\"></i>Achievements
                                </h6>
                                <div id=\"achievements-container\">
                                    <div class=\"collection-items\">
                                        {% for achievement in form.achievements %}
                                            <div class=\"collection-item mb-2\">
                                                <div class=\"input-group\">
                                                    {{ form_widget(achievement, {'attr': {'class': 'form-control'}}) }}
                                                    <button type=\"button\" class=\"btn btn-outline-danger remove-collection-item\">
                                                        <i class=\"fas fa-times\"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    <button type=\"button\" class=\"btn btn-outline-primary add-collection-item\" 
                                            data-prototype=\"{{ form_widget(form.achievements.vars.prototype)|e('html_attr') }}\">
                                        <i class=\"fas fa-plus me-2\"></i>Add Achievement
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class=\"col-md-4\">
                            <!-- Profile Image -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-image me-2\"></i>Profile Image
                                </h6>
                                
                                {% if instructor.profileImage %}
                                    <div class=\"text-center mb-3\">
                                        <img src=\"{{ asset('uploads/instructors/' ~ instructor.profileImage) }}\" 
                                             alt=\"{{ instructor.name }}\" 
                                             class=\"img-fluid rounded-circle\" 
                                             style=\"width: 150px; height: 150px; object-fit: cover; border: 3px solid #1e3c72;\">
                                    </div>
                                {% endif %}
                                
                                {{ form_row(form.profileImageFile, {
                                    'attr': {'class': 'form-control'},
                                    'label_attr': {'class': 'form-label fw-bold'}
                                }) }}
                                <small class=\"form-text text-muted\">
                                    Upload a new image to replace the current one. Recommended size: 300x300px.
                                </small>
                            </div>

                            <!-- Settings -->
                            <div class=\"mb-4\">
                                <h6 class=\"text-muted mb-3\">
                                    <i class=\"fas fa-cog me-2\"></i>Settings
                                </h6>
                                
                                <div class=\"mb-3\">
                                    {{ form_row(form.displayOrder, {
                                        'attr': {'class': 'form-control'},
                                        'label_attr': {'class': 'form-label fw-bold'}
                                    }) }}
                                </div>
                                
                                <div class=\"form-check\">
                                    {{ form_widget(form.isActive, {'attr': {'class': 'form-check-input'}}) }}
                                    {{ form_label(form.isActive, null, {'label_attr': {'class': 'form-check-label'}}) }}
                                </div>
                            </div>
                        </div>
                    </div>

            <!-- Form Footer -->
            <div class=\"card-footer bg-light border-top-0\" style=\"border-radius: 0 0 15px 15px; padding: 1.5rem;\">
                <div class=\"d-flex justify-content-between align-items-center\">
                    <a href=\"{{ path('admin_instructor_index') }}\" class=\"btn btn-outline-secondary\">
                        <i class=\"fas fa-times me-2\"></i>Cancel
                    </a>
                    <div>
                        <a href=\"{{ path('admin_instructor_show', {emailPrefix: instructor.emailPrefix}) }}\" class=\"btn btn-outline-info me-2\">
                            <i class=\"fas fa-eye me-2\"></i>View Details
                        </a>
                        <button type=\"submit\" class=\"btn\" style=\"background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; padding: 0.75rem 2rem; border-radius: 8px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\" onmouseover=\"this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 15px rgba(40, 167, 69, 0.4)';\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(40, 167, 69, 0.3)';\">
                            <i class=\"fas fa-save me-2\"></i>Update Instructor
                        </button>
                    </div>
                </div>
            </div>

            {{ form_end(form) }}
        </div>
    </div>
</div>

<style>
/* Collection form styling */
.collection-item {
    position: relative;
}

.collection-item .input-group {
    margin-bottom: 0.5rem;
}

.add-collection-item {
    border-style: dashed;
    border-width: 2px;
}

.add-collection-item:hover {
    border-style: solid;
}

/* Form styling */
.form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-control:focus {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-color: #1e3c72;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a3461 0%, #245085 100%);
    border-color: #1a3461;
}
</style>

<script>
// Collection form handling for specializations, qualifications, and achievements
document.addEventListener('DOMContentLoaded', function() {
    // Add collection item functionality
    function addCollectionItem(container, prototype) {
        const index = container.children.length;
        const newItem = prototype.replace(/__name__/g, index);
        container.insertAdjacentHTML('beforeend', newItem);
    }

    // Remove collection item functionality
    function removeCollectionItem(button) {
        button.closest('.collection-item').remove();
    }

    // Initialize collection forms
    const collections = ['qualifications', 'achievements'];
    collections.forEach(function(collectionName) {
        const container = document.querySelector(`#\${collectionName}-container`);
        if (container) {
            // Add \"Add\" button functionality
            const addButton = container.querySelector('.add-collection-item');
            if (addButton) {
                addButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    const prototype = this.dataset.prototype;
                    addCollectionItem(container.querySelector('.collection-items'), prototype);
                });
            }

            // Add \"Remove\" button functionality
            container.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-collection-item') || 
                    e.target.closest('.remove-collection-item')) {
                    e.preventDefault();
                    const button = e.target.classList.contains('remove-collection-item') ? 
                                  e.target : e.target.closest('.remove-collection-item');
                    removeCollectionItem(button);
                }
            });
        }
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    }
});
</script>
{% endblock %}
", "admin/instructor/edit.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\instructor\\edit.html.twig");
    }
}
