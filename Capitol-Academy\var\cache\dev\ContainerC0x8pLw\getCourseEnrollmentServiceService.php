<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCourseEnrollmentServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\CourseEnrollmentService' shared autowired service.
     *
     * @return \App\Service\CourseEnrollmentService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'CourseEnrollmentService.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'MoodleService.php';

        $a = ($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container));

        if (isset($container->privates['App\\Service\\CourseEnrollmentService'])) {
            return $container->privates['App\\Service\\CourseEnrollmentService'];
        }
        $b = ($container->privates['mailer.mailer'] ?? $container->load('getMailer_MailerService'));

        if (isset($container->privates['App\\Service\\CourseEnrollmentService'])) {
            return $container->privates['App\\Service\\CourseEnrollmentService'];
        }
        $c = ($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container));

        return $container->privates['App\\Service\\CourseEnrollmentService'] = new \App\Service\CourseEnrollmentService($a, new \App\Service\MoodleService(($container->privates['.debug.http_client'] ?? self::get_Debug_HttpClientService($container)), $c, ($container->privates['parameter_bag'] ??= new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($container)), $b), ($container->privates['App\\Repository\\OrderRepository'] ?? $container->load('getOrderRepositoryService')), $c);
    }
}
