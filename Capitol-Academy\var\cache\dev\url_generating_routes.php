<?php

// This file has been auto-generated by the Symfony Routing Component.

return [
    '_preview_error' => [['code', '_format'], ['_controller' => 'error_controller::preview', '_format' => 'html'], ['code' => '\\d+'], [['variable', '.', '[^/]++', '_format', true], ['variable', '/', '\\d+', 'code', true], ['text', '/_error']], [], [], []],
    '_wdt' => [['token'], ['_controller' => 'web_profiler.controller.profiler::toolbarAction'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/_wdt']], [], [], []],
    '_profiler_home' => [[], ['_controller' => 'web_profiler.controller.profiler::homeAction'], [], [['text', '/_profiler/']], [], [], []],
    '_profiler_search' => [[], ['_controller' => 'web_profiler.controller.profiler::searchAction'], [], [['text', '/_profiler/search']], [], [], []],
    '_profiler_search_bar' => [[], ['_controller' => 'web_profiler.controller.profiler::searchBarAction'], [], [['text', '/_profiler/search_bar']], [], [], []],
    '_profiler_phpinfo' => [[], ['_controller' => 'web_profiler.controller.profiler::phpinfoAction'], [], [['text', '/_profiler/phpinfo']], [], [], []],
    '_profiler_xdebug' => [[], ['_controller' => 'web_profiler.controller.profiler::xdebugAction'], [], [['text', '/_profiler/xdebug']], [], [], []],
    '_profiler_font' => [['fontName'], ['_controller' => 'web_profiler.controller.profiler::fontAction'], [], [['text', '.woff2'], ['variable', '/', '[^/\\.]++', 'fontName', true], ['text', '/_profiler/font']], [], [], []],
    '_profiler_search_results' => [['token'], ['_controller' => 'web_profiler.controller.profiler::searchResultsAction'], [], [['text', '/search/results'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_open_file' => [[], ['_controller' => 'web_profiler.controller.profiler::openAction'], [], [['text', '/_profiler/open']], [], [], []],
    '_profiler' => [['token'], ['_controller' => 'web_profiler.controller.profiler::panelAction'], [], [['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_router' => [['token'], ['_controller' => 'web_profiler.controller.router::panelAction'], [], [['text', '/router'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_exception' => [['token'], ['_controller' => 'web_profiler.controller.exception_panel::body'], [], [['text', '/exception'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    '_profiler_exception_css' => [['token'], ['_controller' => 'web_profiler.controller.exception_panel::stylesheet'], [], [['text', '/exception.css'], ['variable', '/', '[^/]++', 'token', true], ['text', '/_profiler']], [], [], []],
    'admin_category_index' => [[], ['_controller' => 'App\\Controller\\Admin\\CategoryController::index'], [], [['text', '/admin/categories']], [], [], []],
    'admin_category_new' => [[], ['_controller' => 'App\\Controller\\Admin\\CategoryController::new'], [], [['text', '/admin/categories/new']], [], [], []],
    'admin_category_show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'admin_category_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'admin_category_toggle_courses' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleCourses'], [], [['text', '/toggle-courses'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'admin_category_toggle_videos' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleVideos'], [], [['text', '/toggle-videos'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'admin_dashboard_alt' => [[], ['_controller' => 'App\\Controller\\Admin\\DashboardController::index'], [], [['text', '/admin/dashboard']], [], [], []],
    'admin_order_index' => [[], ['_controller' => 'App\\Controller\\Admin\\OrderController::index'], [], [['text', '/admin/orders']], [], [], []],
    'admin_order_show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'admin_order_refund' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::refund'], [], [['text', '/refund'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'admin_order_resend_access' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::resendAccess'], [], [['text', '/resend-access'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'admin_order_export' => [[], ['_controller' => 'App\\Controller\\Admin\\OrderController::export'], [], [['text', '/admin/orders/export']], [], [], []],
    'admin_video_index' => [[], ['_controller' => 'App\\Controller\\Admin\\VideoController::index'], [], [['text', '/admin/videos']], [], [], []],
    'admin_video_new' => [[], ['_controller' => 'App\\Controller\\Admin\\VideoController::new'], [], [['text', '/admin/videos/new']], [], [], []],
    'admin_video_show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'admin_video_edit' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'admin_video_delete' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'admin_video_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'admin_dashboard' => [[], ['_controller' => 'App\\Controller\\AdminController::dashboard'], [], [['text', '/admin/dashboard']], [], [], []],
    'admin_users' => [[], ['_controller' => 'App\\Controller\\AdminController::users'], [], [['text', '/admin/users']], [], [], []],
    'admin_user_show' => [['id'], ['_controller' => 'App\\Controller\\AdminController::userShow'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_user_details' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminController::userDetails'], ['emailPrefix' => '[a-zA-Z0-9\\-_\\.]+'], [['variable', '/', '[a-zA-Z0-9\\-_\\.]+', 'emailPrefix', true], ['text', '/admin/users']], [], [], []],
    'admin_user_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::userEdit'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_user_block' => [['id'], ['_controller' => 'App\\Controller\\AdminController::blockUser'], ['id' => '\\d+'], [['text', '/block'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_user_unblock' => [['id'], ['_controller' => 'App\\Controller\\AdminController::unblockUser'], ['id' => '\\d+'], [['text', '/unblock'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_user_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleUserStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_user_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteUser'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'admin_courses' => [[], ['_controller' => 'App\\Controller\\AdminController::courses'], [], [['text', '/admin/courses']], [], [], []],
    'admin_course_create' => [[], ['_controller' => 'App\\Controller\\AdminController::createCourse'], [], [['text', '/admin/courses/create']], [], [], []],
    'admin_course_edit' => [['code'], ['_controller' => 'App\\Controller\\AdminController::editCourse'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/courses']], [], [], []],
    'admin_course_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteCourse'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/courses']], [], [], []],
    'admin_course_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleCourseStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/courses']], [], [], []],
    'admin_contacts' => [[], ['_controller' => 'App\\Controller\\AdminController::contacts'], [], [['text', '/admin/contacts']], [], [], []],
    'admin_contact_show' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::contactShow'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'admin_contact_preview' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::contactPreview'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'admin_contact_toggle_processed' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::toggleContactProcessed'], [], [['text', '/toggle-processed'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'admin_contact_delete' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::deleteContact'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'admin_test_email' => [[], ['_controller' => 'App\\Controller\\AdminController::testEmail'], [], [['text', '/admin/test-email']], [], [], []],
    'admin_add_admin' => [[], ['_controller' => 'App\\Controller\\AdminController::addAdmin'], [], [['text', '/admin/add-admin']], [], [], []],
    'admin_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleAdminStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/admin']], [], [], []],
    'admin_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteAdmin'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/admin']], [], [], []],
    'admin_course_preview' => [['code'], ['_controller' => 'App\\Controller\\AdminController::previewCourse'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/courses']], [], [], []],
    'admin_profile' => [[], ['_controller' => 'App\\Controller\\AdminController::profile'], [], [['text', '/admin/profile']], [], [], []],
    'admin_plans' => [[], ['_controller' => 'App\\Controller\\AdminController::plans'], [], [['text', '/admin/plans']], [], [], []],
    'admin_plan_create' => [[], ['_controller' => 'App\\Controller\\AdminController::createPlan'], [], [['text', '/admin/plans/create']], [], [], []],
    'admin_plan_edit' => [['code'], ['_controller' => 'App\\Controller\\AdminController::editPlan'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'admin_plan_preview' => [['code'], ['_controller' => 'App\\Controller\\AdminController::previewPlan'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'admin_plan_toggle_status' => [['code'], ['_controller' => 'App\\Controller\\AdminController::togglePlanStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'admin_admins' => [[], ['_controller' => 'App\\Controller\\AdminController::listAdmins'], [], [['text', '/admin/admins']], [], [], []],
    'admin_admin_view' => [['id'], ['_controller' => 'App\\Controller\\AdminController::viewAdmin'], ['id' => '\\d+'], [['text', '/view'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/admin']], [], [], []],
    'admin_admin_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editAdmin'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/admin']], [], [], []],
    'admin_videos' => [[], ['_controller' => 'App\\Controller\\AdminController::videos'], [], [['text', '/admin/videos']], [], [], []],
    'admin_orders' => [[], ['_controller' => 'App\\Controller\\AdminController::orders'], [], [['text', '/admin/orders']], [], [], []],
    'admin_promotional_banners' => [[], ['_controller' => 'App\\Controller\\AdminController::promotionalBanners'], [], [['text', '/admin/promotional-banners']], [], [], []],
    'admin_promotional_banner_create' => [[], ['_controller' => 'App\\Controller\\AdminController::createPromotionalBanner'], [], [['text', '/admin/promotional-banners/create']], [], [], []],
    'admin_promotional_banner_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editPromotionalBanner'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'admin_promotional_banner_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deletePromotionalBanner'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'admin_promotional_banner_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminController::togglePromotionalBannerStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'admin_partners' => [[], ['_controller' => 'App\\Controller\\AdminController::partnersManagement'], [], [['text', '/admin/partners']], [], [], []],
    'admin_partners_create' => [[], ['_controller' => 'App\\Controller\\AdminController::createPartner'], [], [['text', '/admin/partners/create']], [], [], []],
    'admin_partners_show' => [['id'], ['_controller' => 'App\\Controller\\AdminController::showPartner'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'admin_partners_show_by_slug' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::showPartnerBySlug'], ['slug' => '[a-z0-9\\-]+'], [['variable', '/', '[a-z0-9\\-]+', 'slug', true], ['text', '/admin/partners']], [], [], []],
    'admin_partner_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editPartner'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'admin_partners_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deletePartner'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'admin_partners_toggle' => [['id'], ['_controller' => 'App\\Controller\\AdminController::togglePartner'], ['id' => '\\d+'], [['text', '/toggle'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'admin_partners_reorder' => [[], ['_controller' => 'App\\Controller\\AdminController::reorderPartners'], [], [['text', '/admin/partners/reorder']], [], [], []],
    'admin_enrollments_list' => [[], ['_controller' => 'App\\Controller\\AdminController::enrollmentsList'], [], [['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_details' => [['id'], ['_controller' => 'App\\Controller\\AdminController::enrollmentDetails'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_details_by_code' => [['courseCode', 'studentName'], ['_controller' => 'App\\Controller\\AdminController::enrollmentDetailsByCode'], [], [['variable', '-', '[^/]++', 'studentName', true], ['variable', '/', '[^/\\-]++', 'courseCode', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_edit' => [['courseCode', 'studentName'], ['_controller' => 'App\\Controller\\AdminController::editEnrollment'], [], [['text', '/edit'], ['variable', '-', '[^/]++', 'studentName', true], ['variable', '/', '[^/\\-]++', 'courseCode', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_create' => [[], ['_controller' => 'App\\Controller\\AdminController::createEnrollment'], [], [['text', '/admin/enrollments/create']], [], [], []],
    'admin_enrollment_update_progress' => [['id'], ['_controller' => 'App\\Controller\\AdminController::updateEnrollmentProgress'], ['id' => '\\d+'], [['text', '/update-progress'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_mark_completed' => [['id'], ['_controller' => 'App\\Controller\\AdminController::markEnrollmentCompleted'], ['id' => '\\d+'], [['text', '/mark-completed'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_update' => [['id'], ['_controller' => 'App\\Controller\\AdminController::updateEnrollment'], ['id' => '\\d+'], [['text', '/update'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_block' => [['id'], ['_controller' => 'App\\Controller\\AdminController::blockEnrollment'], ['id' => '\\d+'], [['text', '/block'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_unblock' => [['id'], ['_controller' => 'App\\Controller\\AdminController::unblockEnrollment'], ['id' => '\\d+'], [['text', '/unblock'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteEnrollment'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_enrollment_certify' => [['id'], ['_controller' => 'App\\Controller\\AdminController::certifyEnrollment'], ['id' => '\\d+'], [['text', '/certify'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'admin_certifications' => [[], ['_controller' => 'App\\Controller\\AdminController::certifications'], [], [['text', '/admin/certifications']], [], [], []],
    'admin_certification_details' => [['id'], ['_controller' => 'App\\Controller\\AdminController::certificationDetails'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/certifications']], [], [], []],
    'admin_certification_details_by_readable' => [['studentName', 'courseCode'], ['_controller' => 'App\\Controller\\AdminController::certificationDetailsByReadable'], ['studentName' => '[a-z0-9\\-]+', 'courseCode' => '[A-Z0-9]+'], [['variable', '-', '[A-Z0-9]+', 'courseCode', true], ['variable', '/', '[a-z0-9\\-]+', 'studentName', true], ['text', '/admin/certifications']], [], [], []],
    'admin_instructor_index' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::index'], [], [['text', '/admin/instructors/']], [], [], []],
    'admin_instructor_new' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::new'], [], [['text', '/admin/instructors/new']], [], [], []],
    'admin_instructor_show' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminInstructorController::show'], [], [['variable', '/', '[^/]++', 'emailPrefix', true], ['text', '/admin/instructors']], [], [], []],
    'admin_instructor_edit' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminInstructorController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'emailPrefix', true], ['text', '/admin/instructors']], [], [], []],
    'admin_instructor_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'admin_instructor_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'admin_instructor_reorder' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::reorder'], [], [['text', '/admin/instructors/reorder']], [], [], []],
    'admin_instructor_print' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::print'], [], [['text', '/print'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'admin_market_analysis_index' => [[], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::index'], [], [['text', '/admin/market_analysis/']], [], [], []],
    'admin_market_analysis_create' => [[], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::create'], [], [['text', '/admin/market_analysis/create']], [], [], []],
    'admin_market_analysis_edit_readable' => [['slug'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::editBySlug'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_market_analysis_edit' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_market_analysis_show_readable' => [['slug'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::showBySlug'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_market_analysis_show' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::show'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_market_analysis_delete' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_market_analysis_toggle_status' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'admin_login' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::login'], [], [['text', '/admin/login']], [], [], []],
    'admin_login_check' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::loginCheck'], [], [['text', '/admin/login_check']], [], [], []],
    'admin_logout' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::logout'], [], [['text', '/admin/logout']], [], [], []],
    'admin_create_admin' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::createAdmin'], [], [['text', '/admin/create-admin']], [], [], []],
    'app_cart' => [[], ['_controller' => 'App\\Controller\\CartController::index'], [], [['text', '/cart']], [], [], []],
    'app_cart_add' => [[], ['_controller' => 'App\\Controller\\CartController::add'], [], [['text', '/cart/add']], [], [], []],
    'app_cart_remove' => [[], ['_controller' => 'App\\Controller\\CartController::remove'], [], [['text', '/cart/remove']], [], [], []],
    'app_cart_update' => [[], ['_controller' => 'App\\Controller\\CartController::update'], [], [['text', '/cart/update']], [], [], []],
    'app_cart_clear' => [[], ['_controller' => 'App\\Controller\\CartController::clear'], [], [['text', '/cart/clear']], [], [], []],
    'app_cart_count' => [[], ['_controller' => 'App\\Controller\\CartController::count'], [], [['text', '/cart/count']], [], [], []],
    'app_cart_summary' => [[], ['_controller' => 'App\\Controller\\CartController::summary'], [], [['text', '/cart/summary']], [], [], []],
    'app_cart_widget' => [[], ['_controller' => 'App\\Controller\\CartController::widget'], [], [['text', '/cart/widget']], [], [], []],
    'app_cart_validate' => [[], ['_controller' => 'App\\Controller\\CartController::validate'], [], [['text', '/cart/validate']], [], [], []],
    'app_checkout' => [[], ['_controller' => 'App\\Controller\\CheckoutController::index'], [], [['text', '/checkout']], [], [], []],
    'app_checkout_create_order' => [[], ['_controller' => 'App\\Controller\\CheckoutController::createOrder'], [], [['text', '/checkout/create-order']], [], [], []],
    'app_checkout_capture_order' => [[], ['_controller' => 'App\\Controller\\CheckoutController::captureOrder'], [], [['text', '/checkout/capture-order']], [], [], []],
    'app_checkout_success' => [['orderNumber'], ['_controller' => 'App\\Controller\\CheckoutController::success'], [], [['variable', '/', '[^/]++', 'orderNumber', true], ['text', '/checkout/success']], [], [], []],
    'app_checkout_cancel' => [[], ['_controller' => 'App\\Controller\\CheckoutController::cancel'], [], [['text', '/checkout/cancel']], [], [], []],
    'app_checkout_paypal_webhook' => [[], ['_controller' => 'App\\Controller\\CheckoutController::paypalWebhook'], [], [['text', '/checkout/webhook/paypal']], [], [], []],
    'app_contact' => [[], ['_controller' => 'App\\Controller\\ContactController::index'], [], [['text', '/contact']], [], [], []],
    'app_contact_registration' => [[], ['_controller' => 'App\\Controller\\ContactController::registration'], [], [['text', '/contact/registration']], [], [], []],
    'app_contact_instructor' => [[], ['_controller' => 'App\\Controller\\ContactController::instructor'], [], [['text', '/contact/instructor']], [], [], []],
    'app_message' => [[], ['_controller' => 'App\\Controller\\ContactController::message'], [], [['text', '/message']], [], [], []],
    'app_contact_unified' => [[], ['_controller' => 'App\\Controller\\ContactController::unifiedContact'], [], [['text', '/contact/unified']], [], [], []],
    'app_courses_list' => [[], ['_controller' => 'App\\Controller\\CourseController::list'], [], [['text', '/courses/']], [], [], []],
    'app_courses' => [[], ['_controller' => 'App\\Controller\\CourseController::list'], [], [['text', '/courses/']], [], [], []],
    'app_course_show' => [['code'], ['_controller' => 'App\\Controller\\CourseController::show'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses']], [], [], []],
    'app_course_module_show' => [['courseCode', 'moduleCode'], ['_controller' => 'App\\Controller\\CourseController::showModule'], [], [['variable', '/', '[^/]++', 'moduleCode', true], ['variable', '/', '[^/]++', 'courseCode', true], ['text', '/courses']], [], [], []],
    'app_course_fma' => [[], ['_controller' => 'App\\Controller\\CourseController::financialMarkets'], [], [['text', '/courses/financial-markets']], [], [], []],
    'app_course_tec' => [[], ['_controller' => 'App\\Controller\\CourseController::technicalAnalysis'], [], [['text', '/courses/technical-analysis']], [], [], []],
    'app_course_trs' => [[], ['_controller' => 'App\\Controller\\CourseController::tradingStrategies'], [], [['text', '/courses/trading-strategies']], [], [], []],
    'app_course_fun' => [[], ['_controller' => 'App\\Controller\\CourseController::fundamentalAnalysis'], [], [['text', '/courses/fundamental-analysis']], [], [], []],
    'app_course_ssa' => [[], ['_controller' => 'App\\Controller\\CourseController::psychologicalAnalysis'], [], [['text', '/courses/psychological-analysis']], [], [], []],
    'app_course_mma' => [[], ['_controller' => 'App\\Controller\\CourseController::capitalManagement'], [], [['text', '/courses/capital-management']], [], [], []],
    'app_course_rsk' => [[], ['_controller' => 'App\\Controller\\CourseController::riskManagement'], [], [['text', '/courses/risk-management']], [], [], []],
    'app_course_dtr' => [[], ['_controller' => 'App\\Controller\\CourseController::dayTrading'], [], [['text', '/courses/day-trading']], [], [], []],
    'app_course_pro' => [[], ['_controller' => 'App\\Controller\\CourseController::professionalTrader'], [], [['text', '/courses/professional-trader']], [], [], []],
    'app_course_enroll' => [['code'], ['_controller' => 'App\\Controller\\CourseController::enroll'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses/enroll']], [], [], []],
    'app_course_add_to_cart' => [['code'], ['_controller' => 'App\\Controller\\CourseController::addToCart'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses/add-to-cart']], [], [], []],
    'app_user_courses' => [[], ['_controller' => 'App\\Controller\\CourseController::myCourses'], [], [['text', '/courses/my-courses']], [], [], []],
    'app_courses_by_mode' => [['mode'], ['_controller' => 'App\\Controller\\CourseController::byMode'], [], [['variable', '/', '[^/]++', 'mode', true], ['text', '/courses/by-mode']], [], [], []],
    'connect_google_start' => [[], ['_controller' => 'App\\Controller\\GoogleAuthController::connectAction'], [], [['text', '/connect/google']], [], [], []],
    'connect_google_check' => [[], ['_controller' => 'App\\Controller\\GoogleAuthController::connectCheckAction'], [], [['text', '/connect/google/check']], [], [], []],
    'app_home' => [[], ['_controller' => 'App\\Controller\\HomeController::index'], [], [['text', '/']], [], [], []],
    'app_about' => [[], ['_controller' => 'App\\Controller\\HomeController::about'], [], [['text', '/about']], [], [], []],
    'app_partnership' => [[], ['_controller' => 'App\\Controller\\HomeController::partnership'], [], [['text', '/partnership']], [], [], []],
    'app_diplomas' => [[], ['_controller' => 'App\\Controller\\HomeController::diplomas'], [], [['text', '/diplomas']], [], [], []],
    'app_instructors' => [[], ['_controller' => 'App\\Controller\\HomeController::instructors'], [], [['text', '/instructors']], [], [], []],
    'app_executive_program' => [[], ['_controller' => 'App\\Controller\\HomeController::executiveProgram'], [], [['text', '/executive-program']], [], [], []],
    'app_market_analysis' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::index'], [], [['text', '/trading-tools/market-analysis']], [], [], []],
    'app_market_analysis_show_seo' => [['slug'], ['_controller' => 'App\\Controller\\MarketAnalysisController::showBySeoSlug'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/trading-tools/market-analysis']], [], [], []],
    'api_market_analysis_filter' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::filter'], [], [['text', '/api/market-analysis/filter']], [], [], []],
    'api_market_analysis_featured' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getFeatured'], [], [['text', '/api/market-analysis/featured']], [], [], []],
    'api_market_analysis_recent' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getRecent'], [], [['text', '/api/market-analysis/recent']], [], [], []],
    'api_market_analysis_stats' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getStats'], [], [['text', '/api/market-analysis/stats']], [], [], []],
    'app_forgot_password' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::forgotPassword'], [], [['text', '/forgot-password']], [], [], []],
    'app_reset_password' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::resetPassword'], [], [['text', '/reset-password']], [], [], []],
    'app_verify_reset_code' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::verifyResetCode'], [], [['text', '/verify-reset-code']], [], [], []],
    'app_reset_password_merged' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::resetPasswordMerged'], [], [['text', '/reset-password-merged']], [], [], []],
    'course_enroll' => [['code'], ['_controller' => 'App\\Controller\\PaymentController::enrollInCourse'], [], [['text', '/enroll'], ['variable', '/', '[^/]++', 'code', true], ['text', '/course']], [], [], []],
    'payment_success' => [[], ['_controller' => 'App\\Controller\\PaymentController::paymentSuccess'], [], [['text', '/payment/success']], [], [], []],
    'payment_cancel' => [[], ['_controller' => 'App\\Controller\\PaymentController::paymentCancel'], [], [['text', '/payment/cancel']], [], [], []],
    'stripe_webhook' => [[], ['_controller' => 'App\\Controller\\PaymentController::stripeWebhook'], [], [['text', '/stripe/webhook']], [], [], []],
    'app_search' => [[], ['_controller' => 'App\\Controller\\SearchController::search'], [], [['text', '/search']], [], [], []],
    'api_search_autocomplete' => [[], ['_controller' => 'App\\Controller\\SearchController::autocomplete'], [], [['text', '/api/search/autocomplete']], [], [], []],
    'app_login' => [[], ['_controller' => 'App\\Controller\\SecurityController::login'], [], [['text', '/login']], [], [], []],
    'app_register' => [[], ['_controller' => 'App\\Controller\\SecurityController::register'], [], [['text', '/register']], [], [], []],
    'app_logout' => [[], ['_controller' => 'App\\Controller\\SecurityController::logout'], [], [['text', '/logout']], [], [], []],
    'app_user_home' => [[], ['_controller' => 'App\\Controller\\UserController::home'], [], [['text', '/user/home']], [], [], []],
    'app_user_profile' => [[], ['_controller' => 'App\\Controller\\UserController::profile'], [], [['text', '/user/profile']], [], [], []],
    'app_user_orders' => [[], ['_controller' => 'App\\Controller\\UserOrderController::index'], [], [['text', '/user/orders']], [], [], []],
    'app_user_order_show' => [['orderNumber'], ['_controller' => 'App\\Controller\\UserOrderController::show'], [], [['variable', '/', '[^/]++', 'orderNumber', true], ['text', '/user/orders']], [], [], []],
    'app_videos_list' => [[], ['_controller' => 'App\\Controller\\VideoController::list'], [], [['text', '/videos/']], [], [], []],
    'app_videos' => [[], ['_controller' => 'App\\Controller\\VideoController::list'], [], [['text', '/videos/']], [], [], []],
    'app_video_show' => [['id'], ['_controller' => 'App\\Controller\\VideoController::show'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/videos']], [], [], []],
    'App\Controller\Admin\CategoryController::index' => [[], ['_controller' => 'App\\Controller\\Admin\\CategoryController::index'], [], [['text', '/admin/categories']], [], [], []],
    'App\Controller\Admin\CategoryController::new' => [[], ['_controller' => 'App\\Controller\\Admin\\CategoryController::new'], [], [['text', '/admin/categories/new']], [], [], []],
    'App\Controller\Admin\CategoryController::show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'App\Controller\Admin\CategoryController::toggleStatus' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'App\Controller\Admin\CategoryController::toggleCourses' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleCourses'], [], [['text', '/toggle-courses'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'App\Controller\Admin\CategoryController::toggleVideos' => [['id'], ['_controller' => 'App\\Controller\\Admin\\CategoryController::toggleVideos'], [], [['text', '/toggle-videos'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/categories']], [], [], []],
    'App\Controller\Admin\OrderController::index' => [[], ['_controller' => 'App\\Controller\\Admin\\OrderController::index'], [], [['text', '/admin/orders']], [], [], []],
    'App\Controller\Admin\OrderController::show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'App\Controller\Admin\OrderController::refund' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::refund'], [], [['text', '/refund'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'App\Controller\Admin\OrderController::resendAccess' => [['id'], ['_controller' => 'App\\Controller\\Admin\\OrderController::resendAccess'], [], [['text', '/resend-access'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/orders']], [], [], []],
    'App\Controller\Admin\OrderController::export' => [[], ['_controller' => 'App\\Controller\\Admin\\OrderController::export'], [], [['text', '/admin/orders/export']], [], [], []],
    'App\Controller\Admin\VideoController::index' => [[], ['_controller' => 'App\\Controller\\Admin\\VideoController::index'], [], [['text', '/admin/videos']], [], [], []],
    'App\Controller\Admin\VideoController::new' => [[], ['_controller' => 'App\\Controller\\Admin\\VideoController::new'], [], [['text', '/admin/videos/new']], [], [], []],
    'App\Controller\Admin\VideoController::show' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::show'], [], [['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'App\Controller\Admin\VideoController::edit' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'App\Controller\Admin\VideoController::delete' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'App\Controller\Admin\VideoController::toggleStatus' => [['id'], ['_controller' => 'App\\Controller\\Admin\\VideoController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/videos']], [], [], []],
    'App\Controller\AdminController::dashboard' => [[], ['_controller' => 'App\\Controller\\AdminController::dashboard'], [], [['text', '/admin/dashboard']], [], [], []],
    'App\Controller\AdminController::users' => [[], ['_controller' => 'App\\Controller\\AdminController::users'], [], [['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::userShow' => [['id'], ['_controller' => 'App\\Controller\\AdminController::userShow'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::userDetails' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminController::userDetails'], ['emailPrefix' => '[a-zA-Z0-9\\-_\\.]+'], [['variable', '/', '[a-zA-Z0-9\\-_\\.]+', 'emailPrefix', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::userEdit' => [['id'], ['_controller' => 'App\\Controller\\AdminController::userEdit'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::blockUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::blockUser'], ['id' => '\\d+'], [['text', '/block'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::unblockUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::unblockUser'], ['id' => '\\d+'], [['text', '/unblock'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::toggleUserStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleUserStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::deleteUser' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteUser'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/users']], [], [], []],
    'App\Controller\AdminController::courses' => [[], ['_controller' => 'App\\Controller\\AdminController::courses'], [], [['text', '/admin/courses']], [], [], []],
    'App\Controller\AdminController::createCourse' => [[], ['_controller' => 'App\\Controller\\AdminController::createCourse'], [], [['text', '/admin/courses/create']], [], [], []],
    'App\Controller\AdminController::editCourse' => [['code'], ['_controller' => 'App\\Controller\\AdminController::editCourse'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/courses']], [], [], []],
    'App\Controller\AdminController::deleteCourse' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteCourse'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/courses']], [], [], []],
    'App\Controller\AdminController::toggleCourseStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleCourseStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/courses']], [], [], []],
    'App\Controller\AdminController::contacts' => [[], ['_controller' => 'App\\Controller\\AdminController::contacts'], [], [['text', '/admin/contacts']], [], [], []],
    'App\Controller\AdminController::contactShow' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::contactShow'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'App\Controller\AdminController::contactPreview' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::contactPreview'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'App\Controller\AdminController::toggleContactProcessed' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::toggleContactProcessed'], [], [['text', '/toggle-processed'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'App\Controller\AdminController::deleteContact' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::deleteContact'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/contacts']], [], [], []],
    'App\Controller\AdminController::testEmail' => [[], ['_controller' => 'App\\Controller\\AdminController::testEmail'], [], [['text', '/admin/test-email']], [], [], []],
    'App\Controller\AdminController::addAdmin' => [[], ['_controller' => 'App\\Controller\\AdminController::addAdmin'], [], [['text', '/admin/add-admin']], [], [], []],
    'App\Controller\AdminController::toggleAdminStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminController::toggleAdminStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/admin']], [], [], []],
    'App\Controller\AdminController::deleteAdmin' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteAdmin'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/admin']], [], [], []],
    'App\Controller\AdminController::previewCourse' => [['code'], ['_controller' => 'App\\Controller\\AdminController::previewCourse'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/courses']], [], [], []],
    'App\Controller\AdminController::profile' => [[], ['_controller' => 'App\\Controller\\AdminController::profile'], [], [['text', '/admin/profile']], [], [], []],
    'App\Controller\AdminController::plans' => [[], ['_controller' => 'App\\Controller\\AdminController::plans'], [], [['text', '/admin/plans']], [], [], []],
    'App\Controller\AdminController::createPlan' => [[], ['_controller' => 'App\\Controller\\AdminController::createPlan'], [], [['text', '/admin/plans/create']], [], [], []],
    'App\Controller\AdminController::editPlan' => [['code'], ['_controller' => 'App\\Controller\\AdminController::editPlan'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'App\Controller\AdminController::previewPlan' => [['code'], ['_controller' => 'App\\Controller\\AdminController::previewPlan'], [], [['text', '/preview'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'App\Controller\AdminController::togglePlanStatus' => [['code'], ['_controller' => 'App\\Controller\\AdminController::togglePlanStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'code', true], ['text', '/admin/plans']], [], [], []],
    'App\Controller\AdminController::listAdmins' => [[], ['_controller' => 'App\\Controller\\AdminController::listAdmins'], [], [['text', '/admin/admins']], [], [], []],
    'App\Controller\AdminController::viewAdmin' => [['id'], ['_controller' => 'App\\Controller\\AdminController::viewAdmin'], ['id' => '\\d+'], [['text', '/view'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/admin']], [], [], []],
    'App\Controller\AdminController::editAdmin' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editAdmin'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/admin']], [], [], []],
    'App\Controller\AdminController::videos' => [[], ['_controller' => 'App\\Controller\\AdminController::videos'], [], [['text', '/admin/videos']], [], [], []],
    'App\Controller\AdminController::orders' => [[], ['_controller' => 'App\\Controller\\AdminController::orders'], [], [['text', '/admin/orders']], [], [], []],
    'App\Controller\AdminController::promotionalBanners' => [[], ['_controller' => 'App\\Controller\\AdminController::promotionalBanners'], [], [['text', '/admin/promotional-banners']], [], [], []],
    'App\Controller\AdminController::createPromotionalBanner' => [[], ['_controller' => 'App\\Controller\\AdminController::createPromotionalBanner'], [], [['text', '/admin/promotional-banners/create']], [], [], []],
    'App\Controller\AdminController::editPromotionalBanner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editPromotionalBanner'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'App\Controller\AdminController::deletePromotionalBanner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deletePromotionalBanner'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'App\Controller\AdminController::togglePromotionalBannerStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminController::togglePromotionalBannerStatus'], ['id' => '\\d+'], [['text', '/toggle-status'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/promotional-banners']], [], [], []],
    'App\Controller\AdminController::partnersManagement' => [[], ['_controller' => 'App\\Controller\\AdminController::partnersManagement'], [], [['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::createPartner' => [[], ['_controller' => 'App\\Controller\\AdminController::createPartner'], [], [['text', '/admin/partners/create']], [], [], []],
    'App\Controller\AdminController::showPartner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::showPartner'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::showPartnerBySlug' => [['slug'], ['_controller' => 'App\\Controller\\AdminController::showPartnerBySlug'], ['slug' => '[a-z0-9\\-]+'], [['variable', '/', '[a-z0-9\\-]+', 'slug', true], ['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::editPartner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::editPartner'], ['id' => '\\d+'], [['text', '/edit'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::deletePartner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deletePartner'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::togglePartner' => [['id'], ['_controller' => 'App\\Controller\\AdminController::togglePartner'], ['id' => '\\d+'], [['text', '/toggle'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/partners']], [], [], []],
    'App\Controller\AdminController::reorderPartners' => [[], ['_controller' => 'App\\Controller\\AdminController::reorderPartners'], [], [['text', '/admin/partners/reorder']], [], [], []],
    'App\Controller\AdminController::enrollmentsList' => [[], ['_controller' => 'App\\Controller\\AdminController::enrollmentsList'], [], [['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::enrollmentDetails' => [['id'], ['_controller' => 'App\\Controller\\AdminController::enrollmentDetails'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::enrollmentDetailsByCode' => [['courseCode', 'studentName'], ['_controller' => 'App\\Controller\\AdminController::enrollmentDetailsByCode'], [], [['variable', '-', '[^/]++', 'studentName', true], ['variable', '/', '[^/\\-]++', 'courseCode', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::editEnrollment' => [['courseCode', 'studentName'], ['_controller' => 'App\\Controller\\AdminController::editEnrollment'], [], [['text', '/edit'], ['variable', '-', '[^/]++', 'studentName', true], ['variable', '/', '[^/\\-]++', 'courseCode', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::createEnrollment' => [[], ['_controller' => 'App\\Controller\\AdminController::createEnrollment'], [], [['text', '/admin/enrollments/create']], [], [], []],
    'App\Controller\AdminController::updateEnrollmentProgress' => [['id'], ['_controller' => 'App\\Controller\\AdminController::updateEnrollmentProgress'], ['id' => '\\d+'], [['text', '/update-progress'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::markEnrollmentCompleted' => [['id'], ['_controller' => 'App\\Controller\\AdminController::markEnrollmentCompleted'], ['id' => '\\d+'], [['text', '/mark-completed'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::updateEnrollment' => [['id'], ['_controller' => 'App\\Controller\\AdminController::updateEnrollment'], ['id' => '\\d+'], [['text', '/update'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::blockEnrollment' => [['id'], ['_controller' => 'App\\Controller\\AdminController::blockEnrollment'], ['id' => '\\d+'], [['text', '/block'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::unblockEnrollment' => [['id'], ['_controller' => 'App\\Controller\\AdminController::unblockEnrollment'], ['id' => '\\d+'], [['text', '/unblock'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::deleteEnrollment' => [['id'], ['_controller' => 'App\\Controller\\AdminController::deleteEnrollment'], ['id' => '\\d+'], [['text', '/delete'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::certifyEnrollment' => [['id'], ['_controller' => 'App\\Controller\\AdminController::certifyEnrollment'], ['id' => '\\d+'], [['text', '/certify'], ['variable', '/', '\\d+', 'id', true], ['text', '/admin/enrollments']], [], [], []],
    'App\Controller\AdminController::certifications' => [[], ['_controller' => 'App\\Controller\\AdminController::certifications'], [], [['text', '/admin/certifications']], [], [], []],
    'App\Controller\AdminController::certificationDetails' => [['id'], ['_controller' => 'App\\Controller\\AdminController::certificationDetails'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/certifications']], [], [], []],
    'App\Controller\AdminController::certificationDetailsByReadable' => [['studentName', 'courseCode'], ['_controller' => 'App\\Controller\\AdminController::certificationDetailsByReadable'], ['studentName' => '[a-z0-9\\-]+', 'courseCode' => '[A-Z0-9]+'], [['variable', '-', '[A-Z0-9]+', 'courseCode', true], ['variable', '/', '[a-z0-9\\-]+', 'studentName', true], ['text', '/admin/certifications']], [], [], []],
    'App\Controller\AdminInstructorController::index' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::index'], [], [['text', '/admin/instructors/']], [], [], []],
    'App\Controller\AdminInstructorController::new' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::new'], [], [['text', '/admin/instructors/new']], [], [], []],
    'App\Controller\AdminInstructorController::show' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminInstructorController::show'], [], [['variable', '/', '[^/]++', 'emailPrefix', true], ['text', '/admin/instructors']], [], [], []],
    'App\Controller\AdminInstructorController::edit' => [['emailPrefix'], ['_controller' => 'App\\Controller\\AdminInstructorController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'emailPrefix', true], ['text', '/admin/instructors']], [], [], []],
    'App\Controller\AdminInstructorController::delete' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'App\Controller\AdminInstructorController::toggleStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'App\Controller\AdminInstructorController::reorder' => [[], ['_controller' => 'App\\Controller\\AdminInstructorController::reorder'], [], [['text', '/admin/instructors/reorder']], [], [], []],
    'App\Controller\AdminInstructorController::print' => [['id'], ['_controller' => 'App\\Controller\\AdminInstructorController::print'], [], [['text', '/print'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/instructors']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::index' => [[], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::index'], [], [['text', '/admin/market_analysis/']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::create' => [[], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::create'], [], [['text', '/admin/market_analysis/create']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::editBySlug' => [['slug'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::editBySlug'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::edit' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::edit'], [], [['text', '/edit'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::showBySlug' => [['slug'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::showBySlug'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::show' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::show'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::delete' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::delete'], [], [['text', '/delete'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminMarketAnalysisController::toggleStatus' => [['id'], ['_controller' => 'App\\Controller\\AdminMarketAnalysisController::toggleStatus'], [], [['text', '/toggle-status'], ['variable', '/', '[^/]++', 'id', true], ['text', '/admin/market_analysis']], [], [], []],
    'App\Controller\AdminSecurityController::login' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::login'], [], [['text', '/admin/login']], [], [], []],
    'App\Controller\AdminSecurityController::loginCheck' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::loginCheck'], [], [['text', '/admin/login_check']], [], [], []],
    'App\Controller\AdminSecurityController::logout' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::logout'], [], [['text', '/admin/logout']], [], [], []],
    'App\Controller\AdminSecurityController::createAdmin' => [[], ['_controller' => 'App\\Controller\\AdminSecurityController::createAdmin'], [], [['text', '/admin/create-admin']], [], [], []],
    'App\Controller\CartController::index' => [[], ['_controller' => 'App\\Controller\\CartController::index'], [], [['text', '/cart']], [], [], []],
    'App\Controller\CartController::add' => [[], ['_controller' => 'App\\Controller\\CartController::add'], [], [['text', '/cart/add']], [], [], []],
    'App\Controller\CartController::remove' => [[], ['_controller' => 'App\\Controller\\CartController::remove'], [], [['text', '/cart/remove']], [], [], []],
    'App\Controller\CartController::update' => [[], ['_controller' => 'App\\Controller\\CartController::update'], [], [['text', '/cart/update']], [], [], []],
    'App\Controller\CartController::clear' => [[], ['_controller' => 'App\\Controller\\CartController::clear'], [], [['text', '/cart/clear']], [], [], []],
    'App\Controller\CartController::count' => [[], ['_controller' => 'App\\Controller\\CartController::count'], [], [['text', '/cart/count']], [], [], []],
    'App\Controller\CartController::summary' => [[], ['_controller' => 'App\\Controller\\CartController::summary'], [], [['text', '/cart/summary']], [], [], []],
    'App\Controller\CartController::widget' => [[], ['_controller' => 'App\\Controller\\CartController::widget'], [], [['text', '/cart/widget']], [], [], []],
    'App\Controller\CartController::validate' => [[], ['_controller' => 'App\\Controller\\CartController::validate'], [], [['text', '/cart/validate']], [], [], []],
    'App\Controller\CheckoutController::index' => [[], ['_controller' => 'App\\Controller\\CheckoutController::index'], [], [['text', '/checkout']], [], [], []],
    'App\Controller\CheckoutController::createOrder' => [[], ['_controller' => 'App\\Controller\\CheckoutController::createOrder'], [], [['text', '/checkout/create-order']], [], [], []],
    'App\Controller\CheckoutController::captureOrder' => [[], ['_controller' => 'App\\Controller\\CheckoutController::captureOrder'], [], [['text', '/checkout/capture-order']], [], [], []],
    'App\Controller\CheckoutController::success' => [['orderNumber'], ['_controller' => 'App\\Controller\\CheckoutController::success'], [], [['variable', '/', '[^/]++', 'orderNumber', true], ['text', '/checkout/success']], [], [], []],
    'App\Controller\CheckoutController::cancel' => [[], ['_controller' => 'App\\Controller\\CheckoutController::cancel'], [], [['text', '/checkout/cancel']], [], [], []],
    'App\Controller\CheckoutController::paypalWebhook' => [[], ['_controller' => 'App\\Controller\\CheckoutController::paypalWebhook'], [], [['text', '/checkout/webhook/paypal']], [], [], []],
    'App\Controller\ContactController::index' => [[], ['_controller' => 'App\\Controller\\ContactController::index'], [], [['text', '/contact']], [], [], []],
    'App\Controller\ContactController::registration' => [[], ['_controller' => 'App\\Controller\\ContactController::registration'], [], [['text', '/contact/registration']], [], [], []],
    'App\Controller\ContactController::instructor' => [[], ['_controller' => 'App\\Controller\\ContactController::instructor'], [], [['text', '/contact/instructor']], [], [], []],
    'App\Controller\ContactController::message' => [[], ['_controller' => 'App\\Controller\\ContactController::message'], [], [['text', '/message']], [], [], []],
    'App\Controller\ContactController::unifiedContact' => [[], ['_controller' => 'App\\Controller\\ContactController::unifiedContact'], [], [['text', '/contact/unified']], [], [], []],
    'App\Controller\CourseController::show' => [['code'], ['_controller' => 'App\\Controller\\CourseController::show'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses']], [], [], []],
    'App\Controller\CourseController::showModule' => [['courseCode', 'moduleCode'], ['_controller' => 'App\\Controller\\CourseController::showModule'], [], [['variable', '/', '[^/]++', 'moduleCode', true], ['variable', '/', '[^/]++', 'courseCode', true], ['text', '/courses']], [], [], []],
    'App\Controller\CourseController::financialMarkets' => [[], ['_controller' => 'App\\Controller\\CourseController::financialMarkets'], [], [['text', '/courses/financial-markets']], [], [], []],
    'App\Controller\CourseController::technicalAnalysis' => [[], ['_controller' => 'App\\Controller\\CourseController::technicalAnalysis'], [], [['text', '/courses/technical-analysis']], [], [], []],
    'App\Controller\CourseController::tradingStrategies' => [[], ['_controller' => 'App\\Controller\\CourseController::tradingStrategies'], [], [['text', '/courses/trading-strategies']], [], [], []],
    'App\Controller\CourseController::fundamentalAnalysis' => [[], ['_controller' => 'App\\Controller\\CourseController::fundamentalAnalysis'], [], [['text', '/courses/fundamental-analysis']], [], [], []],
    'App\Controller\CourseController::psychologicalAnalysis' => [[], ['_controller' => 'App\\Controller\\CourseController::psychologicalAnalysis'], [], [['text', '/courses/psychological-analysis']], [], [], []],
    'App\Controller\CourseController::capitalManagement' => [[], ['_controller' => 'App\\Controller\\CourseController::capitalManagement'], [], [['text', '/courses/capital-management']], [], [], []],
    'App\Controller\CourseController::riskManagement' => [[], ['_controller' => 'App\\Controller\\CourseController::riskManagement'], [], [['text', '/courses/risk-management']], [], [], []],
    'App\Controller\CourseController::dayTrading' => [[], ['_controller' => 'App\\Controller\\CourseController::dayTrading'], [], [['text', '/courses/day-trading']], [], [], []],
    'App\Controller\CourseController::professionalTrader' => [[], ['_controller' => 'App\\Controller\\CourseController::professionalTrader'], [], [['text', '/courses/professional-trader']], [], [], []],
    'App\Controller\CourseController::enroll' => [['code'], ['_controller' => 'App\\Controller\\CourseController::enroll'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses/enroll']], [], [], []],
    'App\Controller\CourseController::addToCart' => [['code'], ['_controller' => 'App\\Controller\\CourseController::addToCart'], [], [['variable', '/', '[^/]++', 'code', true], ['text', '/courses/add-to-cart']], [], [], []],
    'App\Controller\CourseController::myCourses' => [[], ['_controller' => 'App\\Controller\\CourseController::myCourses'], [], [['text', '/courses/my-courses']], [], [], []],
    'App\Controller\CourseController::byMode' => [['mode'], ['_controller' => 'App\\Controller\\CourseController::byMode'], [], [['variable', '/', '[^/]++', 'mode', true], ['text', '/courses/by-mode']], [], [], []],
    'App\Controller\GoogleAuthController::connectAction' => [[], ['_controller' => 'App\\Controller\\GoogleAuthController::connectAction'], [], [['text', '/connect/google']], [], [], []],
    'App\Controller\GoogleAuthController::connectCheckAction' => [[], ['_controller' => 'App\\Controller\\GoogleAuthController::connectCheckAction'], [], [['text', '/connect/google/check']], [], [], []],
    'App\Controller\HomeController::index' => [[], ['_controller' => 'App\\Controller\\HomeController::index'], [], [['text', '/']], [], [], []],
    'App\Controller\HomeController::about' => [[], ['_controller' => 'App\\Controller\\HomeController::about'], [], [['text', '/about']], [], [], []],
    'App\Controller\HomeController::partnership' => [[], ['_controller' => 'App\\Controller\\HomeController::partnership'], [], [['text', '/partnership']], [], [], []],
    'App\Controller\HomeController::diplomas' => [[], ['_controller' => 'App\\Controller\\HomeController::diplomas'], [], [['text', '/diplomas']], [], [], []],
    'App\Controller\HomeController::instructors' => [[], ['_controller' => 'App\\Controller\\HomeController::instructors'], [], [['text', '/instructors']], [], [], []],
    'App\Controller\HomeController::executiveProgram' => [[], ['_controller' => 'App\\Controller\\HomeController::executiveProgram'], [], [['text', '/executive-program']], [], [], []],
    'App\Controller\MarketAnalysisController::index' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::index'], [], [['text', '/trading-tools/market-analysis']], [], [], []],
    'App\Controller\MarketAnalysisController::showBySeoSlug' => [['slug'], ['_controller' => 'App\\Controller\\MarketAnalysisController::showBySeoSlug'], [], [['variable', '/', '[^/]++', 'slug', true], ['text', '/trading-tools/market-analysis']], [], [], []],
    'App\Controller\MarketAnalysisController::filter' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::filter'], [], [['text', '/api/market-analysis/filter']], [], [], []],
    'App\Controller\MarketAnalysisController::getFeatured' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getFeatured'], [], [['text', '/api/market-analysis/featured']], [], [], []],
    'App\Controller\MarketAnalysisController::getRecent' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getRecent'], [], [['text', '/api/market-analysis/recent']], [], [], []],
    'App\Controller\MarketAnalysisController::getStats' => [[], ['_controller' => 'App\\Controller\\MarketAnalysisController::getStats'], [], [['text', '/api/market-analysis/stats']], [], [], []],
    'App\Controller\PasswordResetController::forgotPassword' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::forgotPassword'], [], [['text', '/forgot-password']], [], [], []],
    'App\Controller\PasswordResetController::resetPassword' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::resetPassword'], [], [['text', '/reset-password']], [], [], []],
    'App\Controller\PasswordResetController::verifyResetCode' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::verifyResetCode'], [], [['text', '/verify-reset-code']], [], [], []],
    'App\Controller\PasswordResetController::resetPasswordMerged' => [[], ['_controller' => 'App\\Controller\\PasswordResetController::resetPasswordMerged'], [], [['text', '/reset-password-merged']], [], [], []],
    'App\Controller\PaymentController::enrollInCourse' => [['code'], ['_controller' => 'App\\Controller\\PaymentController::enrollInCourse'], [], [['text', '/enroll'], ['variable', '/', '[^/]++', 'code', true], ['text', '/course']], [], [], []],
    'App\Controller\PaymentController::paymentSuccess' => [[], ['_controller' => 'App\\Controller\\PaymentController::paymentSuccess'], [], [['text', '/payment/success']], [], [], []],
    'App\Controller\PaymentController::paymentCancel' => [[], ['_controller' => 'App\\Controller\\PaymentController::paymentCancel'], [], [['text', '/payment/cancel']], [], [], []],
    'App\Controller\PaymentController::stripeWebhook' => [[], ['_controller' => 'App\\Controller\\PaymentController::stripeWebhook'], [], [['text', '/stripe/webhook']], [], [], []],
    'App\Controller\SearchController::search' => [[], ['_controller' => 'App\\Controller\\SearchController::search'], [], [['text', '/search']], [], [], []],
    'App\Controller\SearchController::autocomplete' => [[], ['_controller' => 'App\\Controller\\SearchController::autocomplete'], [], [['text', '/api/search/autocomplete']], [], [], []],
    'App\Controller\SecurityController::login' => [[], ['_controller' => 'App\\Controller\\SecurityController::login'], [], [['text', '/login']], [], [], []],
    'App\Controller\SecurityController::register' => [[], ['_controller' => 'App\\Controller\\SecurityController::register'], [], [['text', '/register']], [], [], []],
    'App\Controller\SecurityController::logout' => [[], ['_controller' => 'App\\Controller\\SecurityController::logout'], [], [['text', '/logout']], [], [], []],
    'App\Controller\UserController::home' => [[], ['_controller' => 'App\\Controller\\UserController::home'], [], [['text', '/user/home']], [], [], []],
    'App\Controller\UserController::profile' => [[], ['_controller' => 'App\\Controller\\UserController::profile'], [], [['text', '/user/profile']], [], [], []],
    'App\Controller\UserOrderController::index' => [[], ['_controller' => 'App\\Controller\\UserOrderController::index'], [], [['text', '/user/orders']], [], [], []],
    'App\Controller\UserOrderController::show' => [['orderNumber'], ['_controller' => 'App\\Controller\\UserOrderController::show'], [], [['variable', '/', '[^/]++', 'orderNumber', true], ['text', '/user/orders']], [], [], []],
    'App\Controller\VideoController::show' => [['id'], ['_controller' => 'App\\Controller\\VideoController::show'], ['id' => '\\d+'], [['variable', '/', '\\d+', 'id', true], ['text', '/videos']], [], [], []],
];
