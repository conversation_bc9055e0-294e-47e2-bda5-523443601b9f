<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capitol Academy - Forgot Password</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/logos/logo-round.png') }}">
    <link rel="shortcut icon" href="{{ asset('images/logos/logo-round.png') }}">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.08)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        .logo {
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            filter: brightness(0) invert(1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            backdrop-filter: blur(10px);
        }

        .logo:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .logo-fallback {
            display: none;
            width: 85px;
            height: 85px;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--ca-white);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .logo-fallback:hover {
            transform: scale(1.08) rotate(5deg);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            font-size: 1.2rem;
            opacity: 0.95;
            margin: 0;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Right Side - Form */
        .login-form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--ca-dark-gray);
            margin: 0 0 12px 0;
            letter-spacing: -0.3px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            margin: 0;
            font-weight: 400;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            border: none;
            border-bottom: 2px solid #e8ecef;
            background: transparent;
            font-size: 1.05rem;
            color: var(--ca-dark-gray);
            outline: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            height: calc(1.6em + 1.25rem + 4px);
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
            background: transparent !important;
            transform: translateY(-1px);
        }

        .floating-input:hover:not(:focus) {
            border-bottom-color: var(--ca-focus-blue-border);
        }

        .floating-input:focus::placeholder {
            opacity: 0;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.05rem;
            color: var(--ca-medium-gray);
            pointer-events: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            transform-origin: left top;
        }

        .floating-input:focus + .floating-label,
        .floating-input:valid + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: -12px;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 700;
            transform: scale(0.9);
            text-shadow: 0 1px 2px rgba(1, 26, 45, 0.1);
        }

        .floating-input:hover:not(:focus) + .floating-label {
            color: var(--ca-focus-blue-border);
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--ca-focus-blue) 0%, var(--ca-secondary-blue) 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(1, 26, 45, 0.3);
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        .floating-input:hover:not(:focus) ~ .input-border {
            width: 30%;
            background: linear-gradient(135deg, var(--ca-focus-blue-border) 0%, rgba(26, 52, 97, 0.5) 100%);
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            padding: 18px 35px;
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
            border: none;
            border-radius: 60px;
            color: var(--ca-white);
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin: 35px 0 25px 0;
            text-transform: none;
            letter-spacing: 0.3px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.25);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.4s;
            animation-fill-mode: both;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(1, 26, 45, 0.4);
        }

        .login-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 8px 20px rgba(1, 26, 45, 0.3);
        }

        .btn-icon {
            font-size: 1.1rem;
            transition: transform 0.3s ease;
        }

        .login-btn:hover .btn-icon {
            transform: scale(1.1);
        }

        /* Alert Styles */
        .alert {
            padding: 18px 22px;
            margin-bottom: 25px;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 12px;
            animation: fadeInUp 0.6s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .alert-danger {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            color: #991b1b;
            border-left: 4px solid #dc2626;
        }

        .alert-success {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #166534;
            border-left: 4px solid #16a34a;
        }

        .alert i {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            margin-top: 35px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
        }

        .back-link {
            color: var(--ca-medium-gray);
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 25px;
            background: rgba(1, 26, 45, 0.05);
        }

        .back-link:hover {
            color: var(--ca-focus-blue);
            text-decoration: none;
            background: rgba(1, 26, 45, 0.1);
            transform: translateY(-1px);
        }

        .back-link i {
            transition: transform 0.3s ease;
        }

        .back-link:hover i {
            transform: translateX(-2px);
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .brand-logo-round:hover {
            transform: scale(1.05) rotate(2deg);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .login-wrapper {
                max-width: 900px;
            }

            .login-branding {
                padding: 35px 25px;
            }

            .login-form-section {
                padding: 50px 35px;
            }
        }

        @media (max-width: 768px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                max-width: 500px;
                min-height: auto;
            }

            .login-branding {
                padding: 40px 30px;
                min-height: 300px;
            }

            .logo {
                width: 70px;
                height: 70px;
                margin-bottom: 20px;
            }

            .brand-title {
                font-size: 2.4rem;
            }

            .brand-subtitle {
                font-size: 1.1rem;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .form-title {
                font-size: 1.9rem;
            }

            .floating-input {
                padding: 20px 0 10px 0;
                font-size: 1rem;
            }

            .floating-label {
                font-size: 1rem;
                top: 20px;
            }

            .floating-input:focus + .floating-label,
            .floating-input:valid + .floating-label,
            .floating-input:not(:placeholder-shown) + .floating-label {
                top: -10px;
                font-size: 0.8rem;
            }

            .login-btn {
                padding: 16px 30px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }

            .login-branding {
                padding: 30px 20px;
                min-height: 250px;
            }

            .logo {
                width: 60px;
                height: 60px;
                margin-bottom: 15px;
            }

            .brand-title {
                font-size: 2rem;
            }

            .brand-subtitle {
                font-size: 1rem;
            }

            .login-form-section {
                padding: 30px 20px;
            }

            .form-title {
                font-size: 1.7rem;
            }

            .form-container {
                max-width: 100%;
            }

            .floating-input {
                font-size: 0.95rem;
            }

            .login-btn {
                padding: 16px 25px;
                font-size: 0.95rem;
            }

            .alert {
                padding: 15px 18px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-wrapper">
            <!-- Left Side - Branding -->
            <div class="login-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <img src="{{ asset('images/logos/logo-round.png') }}" alt="Capitol Academy" class="brand-logo-round">
                        <h1 class="brand-title">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - Forgot Password Form -->
            <div class="login-form-section">
                <div class="form-container">
                    <!-- Form Header -->
                    <div class="form-header">
                        <h2 class="form-title">Forgot Password</h2>
                        <p class="form-subtitle">Enter your email to receive a verification code</p>
                    </div>

                    <!-- Error Messages -->
                    {% if error %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>{{ error }}</span>
                        </div>
                    {% endif %}

                    <!-- Forgot Password Form -->
                    <form method="post" action="{{ path('app_forgot_password') }}">
                        <!-- Email Field -->
                        <div class="floating-label-group">
                            <input type="email"
                                   class="floating-input"
                                   name="email"
                                   placeholder=" "
                                   required
                                   autofocus>
                            <label class="floating-label">
                                <i class="fas fa-envelope" style="color: var(--ca-focus-blue); margin-right: 8px;"></i>
                                Email Address
                            </label>
                            <div class="input-border"></div>
                        </div>

                        <!-- CSRF Token -->
                        <input type="hidden" name="_token" value="{{ csrf_token('forgot_password') }}">

                        <!-- Send Code Button -->
                        <button type="submit" class="login-btn">
                            <i class="fas fa-paper-plane btn-icon"></i>
                            <span>Send Verification Code</span>
                        </button>
                    </form>

                    <!-- Form Footer -->
                    <div class="form-footer">
                        <a href="{{ path('app_login') }}" class="back-link">
                            <i class="fas fa-arrow-left me-2"></i>Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
