<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPasswordResetTokenRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\PasswordResetTokenRepository' shared autowired service.
     *
     * @return \App\Repository\PasswordResetTokenRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'PasswordResetTokenRepository.php';

        return $container->privates['App\\Repository\\PasswordResetTokenRepository'] = new \App\Repository\PasswordResetTokenRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
