<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCourseService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.jqHm011.App\Entity\Course' shared service.
     *
     * @return \App\Entity\Course
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.jqHm011": it needs an instance of "App\\Entity\\Course" but this type has been excluded in "config/services.yaml".');
    }
}
