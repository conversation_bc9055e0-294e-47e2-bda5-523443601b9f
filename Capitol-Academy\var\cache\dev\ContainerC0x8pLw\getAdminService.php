<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.mCbvPMW.App\Entity\Admin' shared service.
     *
     * @return \App\Entity\Admin
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.mCbvPMW": it needs an instance of "App\\Entity\\Admin" but this type has been excluded in "config/services.yaml".');
    }
}
