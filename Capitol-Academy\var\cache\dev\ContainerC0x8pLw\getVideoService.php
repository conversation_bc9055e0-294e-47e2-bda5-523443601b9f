<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getVideoService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.bki8G0J.App\Entity\Video' shared service.
     *
     * @return \App\Entity\Video
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.bki8G0J": it needs an instance of "App\\Entity\\Video" but this type has been excluded in "config/services.yaml".');
    }
}
