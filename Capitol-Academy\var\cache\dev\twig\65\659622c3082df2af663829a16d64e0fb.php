<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* security/reset_password_merged.html.twig */
class __TwigTemplate_c9e887c153f8852db12b1a1e1311498e extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/reset_password_merged.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "security/reset_password_merged.html.twig"));

        // line 1
        yield "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Reset Password</title>

    <!-- Favicon -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"";
        // line 9
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">
    <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"";
        // line 11
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">
    <link rel=\"shortcut icon\" href=\"";
        // line 12
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\">

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .brand-logo-round:hover {
            transform: scale(1.05) rotate(2deg);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Right Side - Form */
        .login-form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--ca-primary-blue);
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.5;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--ca-dark-gray);
            background: transparent;
            border: none;
            border-bottom: 2px solid #e1e5e9;
            outline: none;
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: 0;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.1rem;
            color: var(--ca-medium-gray);
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
            display: flex;
            align-items: center;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--ca-focus-blue);
            transition: width 0.3s ease;
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        /* Submit Button */
        .login-btn {
            width: 100%;
            padding: 20px 30px;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(169, 4, 24, 0.4);
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
        }

        .login-btn:active {
            transform: translateY(0);
            box-shadow: 0 6px 20px rgba(169, 4, 24, 0.3);
        }

        .btn-icon {
            font-size: 1rem;
            transition: transform 0.3s ease;
        }

        .login-btn:hover .btn-icon {
            transform: translateX(2px);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            margin-bottom: 25px;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.95rem;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: rgba(169, 4, 24, 0.1);
            color: var(--ca-accent-red-dark);
            border: 1px solid rgba(169, 4, 24, 0.2);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"";
        // line 399
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl("images/logos/logo-round.png"), "html", null, true);
        yield "\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - Reset Password Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Reset Password</h2>
                        <p class=\"form-subtitle\">Enter your verification code and new password</p>
                    </div>

                    <!-- Error Alert -->
                    ";
        // line 415
        if ((($tmp = (isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 415, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 416
            yield "                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            ";
            // line 418
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["error"]) || array_key_exists("error", $context) ? $context["error"] : (function () { throw new RuntimeError('Variable "error" does not exist.', 418, $this->source); })()), "html", null, true);
            yield "
                        </div>
                    ";
        }
        // line 421
        yield "
                    <!-- Reset Password Form -->
                    <form method=\"post\" action=\"";
        // line 423
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_reset_password_merged");
        yield "\">
                        <!-- Email Field (Hidden) -->
                        <input type=\"hidden\" name=\"email\" value=\"";
        // line 425
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((isset($context["email"]) || array_key_exists("email", $context) ? $context["email"] : (function () { throw new RuntimeError('Variable "email" does not exist.', 425, $this->source); })()), "html", null, true);
        yield "\">

                        <!-- Code Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"text\"
                                   class=\"floating-input\"
                                   name=\"code\"
                                   placeholder=\" \"
                                   maxlength=\"6\"
                                   pattern=\"[0-9]{6}\"
                                   required
                                   autofocus>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-key\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                6-Digit Verification Code
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- New Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"new_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"confirm_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"";
        // line 475
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken("reset_password_merged"), "html", null, true);
        yield "\">

                        <!-- Change Password Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-key btn-icon\"></i>
                            <span>Change Password</span>
                        </button>
                    </form>

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"";
        // line 486
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_forgot_password");
        yield "\" class=\"back-link\">
                            <i class=\"fas fa-envelope me-2\"></i>Change Email
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "security/reset_password_merged.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  567 => 486,  553 => 475,  500 => 425,  495 => 423,  491 => 421,  485 => 418,  481 => 416,  479 => 415,  460 => 399,  70 => 12,  66 => 11,  62 => 10,  58 => 9,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Capitol Academy - Reset Password</title>

    <!-- Favicon -->
    <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"{{ asset('images/logos/logo-round.png') }}\">
    <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"{{ asset('images/logos/logo-round.png') }}\">
    <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"{{ asset('images/logos/logo-round.png') }}\">
    <link rel=\"shortcut icon\" href=\"{{ asset('images/logos/logo-round.png') }}\">

    <!-- Google Fonts -->
    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap\" rel=\"stylesheet\">
    <!-- Font Awesome -->
    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">
    <!-- Bootstrap 5 -->
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">

    <style>
        /* Capitol Academy Brand Colors */
        :root {
            --ca-primary-blue: #011a2d;
            --ca-secondary-blue: #1a3461;
            --ca-accent-red: #a90418;
            --ca-accent-red-dark: #8b0314;
            --ca-dark-gray: #343a40;
            --ca-medium-gray: #6c757d;
            --ca-light-gray: #f8f9fa;
            --ca-white: #ffffff;
            --ca-success-green: #28a745;
            --ca-warning-orange: #ffc107;
            --ca-focus-blue: #011a2d;
            --ca-focus-blue-light: rgba(1, 26, 45, 0.1);
            --ca-focus-blue-border: rgba(1, 26, 45, 0.3);
            --ca-professional-shadow: rgba(1, 26, 45, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            overflow-x: hidden;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.05)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            max-width: 1000px;
            width: 100%;
            background: var(--ca-white);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            min-height: 600px;
            position: relative;
            z-index: 1;
        }

        /* Left Side - Branding */
        .login-branding {
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            padding: 40px 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-branding::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.08)\" stroke-width=\"1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');
            opacity: 0.4;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .branding-content {
            position: relative;
            z-index: 2;
        }

        .logo-section {
            text-align: center;
        }

        .brand-logo-round {
            width: 300px;
            height: 300px;
            margin-bottom: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .brand-logo-round:hover {
            transform: scale(1.05) rotate(2deg);
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        .brand-title {
            font-size: 2.8rem;
            font-weight: 800;
            margin: 0 0 15px 0;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Right Side - Form */
        .login-form-section {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--ca-white) 0%, #fafbfc 100%);
        }

        .form-container {
            max-width: 360px;
            width: 100%;
            margin: 0 auto;
        }

        .form-header {
            text-align: center;
            margin-bottom: 35px;
            animation: fadeInUp 0.6s ease;
        }

        .form-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--ca-primary-blue);
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .form-subtitle {
            color: var(--ca-medium-gray);
            font-size: 1rem;
            font-weight: 500;
            line-height: 1.5;
        }

        /* Floating Label Inputs */
        .floating-label-group {
            position: relative;
            margin-bottom: 28px;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.2s;
            animation-fill-mode: both;
        }

        .floating-input {
            width: 100%;
            padding: 22px 0 12px 0;
            font-size: 1.1rem;
            font-weight: 500;
            color: var(--ca-dark-gray);
            background: transparent;
            border: none;
            border-bottom: 2px solid #e1e5e9;
            outline: none;
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        }

        .floating-input:focus {
            border-bottom-color: var(--ca-focus-blue);
        }

        .floating-input:focus + .floating-label,
        .floating-input:not(:placeholder-shown) + .floating-label {
            top: 0;
            font-size: 0.85rem;
            color: var(--ca-focus-blue);
            font-weight: 600;
        }

        .floating-label {
            position: absolute;
            top: 22px;
            left: 0;
            font-size: 1.1rem;
            color: var(--ca-medium-gray);
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
            display: flex;
            align-items: center;
        }

        .input-border {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--ca-focus-blue);
            transition: width 0.3s ease;
        }

        .floating-input:focus ~ .input-border {
            width: 100%;
        }

        /* Submit Button */
        .login-btn {
            width: 100%;
            padding: 20px 30px;
            background: linear-gradient(135deg, var(--ca-primary-blue) 0%, var(--ca-secondary-blue) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 35px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(169, 4, 24, 0.3);
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.6s;
            animation-fill-mode: both;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(169, 4, 24, 0.4);
            background: linear-gradient(135deg, var(--ca-accent-red) 0%, var(--ca-accent-red-dark) 100%);
        }

        .login-btn:active {
            transform: translateY(0);
            box-shadow: 0 6px 20px rgba(169, 4, 24, 0.3);
        }

        .btn-icon {
            font-size: 1rem;
            transition: transform 0.3s ease;
        }

        .login-btn:hover .btn-icon {
            transform: translateX(2px);
        }

        /* Form Footer */
        .form-footer {
            text-align: center;
            animation: fadeInUp 0.6s ease;
            animation-delay: 0.8s;
            animation-fill-mode: both;
        }

        .back-link {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 8px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            border: 2px solid;
            text-align: center;
            min-width: 140px;
            background: white;
            color: var(--ca-focus-blue);
            border-color: var(--ca-focus-blue);
        }

        .back-link:hover {
            background: var(--ca-focus-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(1, 26, 45, 0.3);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Alert Styles */
        .alert {
            padding: 15px 20px;
            margin-bottom: 25px;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.95rem;
            animation: slideInDown 0.5s ease;
        }

        .alert-danger {
            background: rgba(169, 4, 24, 0.1);
            color: var(--ca-accent-red-dark);
            border: 1px solid rgba(169, 4, 24, 0.2);
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class=\"login-container\">
        <div class=\"login-wrapper\">
            <!-- Left Side - Branding -->
            <div class=\"login-branding\">
                <div class=\"branding-content\">
                    <div class=\"logo-section\">
                        <img src=\"{{ asset('images/logos/logo-round.png') }}\" alt=\"Capitol Academy\" class=\"brand-logo-round\">
                        <h1 class=\"brand-title\">Capitol Academy</h1>
                    </div>
                </div>
            </div>

            <!-- Right Side - Reset Password Form -->
            <div class=\"login-form-section\">
                <div class=\"form-container\">
                    <!-- Form Header -->
                    <div class=\"form-header\">
                        <h2 class=\"form-title\">Reset Password</h2>
                        <p class=\"form-subtitle\">Enter your verification code and new password</p>
                    </div>

                    <!-- Error Alert -->
                    {% if error %}
                        <div class=\"alert alert-danger\">
                            <i class=\"fas fa-exclamation-triangle\"></i>
                            {{ error }}
                        </div>
                    {% endif %}

                    <!-- Reset Password Form -->
                    <form method=\"post\" action=\"{{ path('app_reset_password_merged') }}\">
                        <!-- Email Field (Hidden) -->
                        <input type=\"hidden\" name=\"email\" value=\"{{ email }}\">

                        <!-- Code Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"text\"
                                   class=\"floating-input\"
                                   name=\"code\"
                                   placeholder=\" \"
                                   maxlength=\"6\"
                                   pattern=\"[0-9]{6}\"
                                   required
                                   autofocus>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-key\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                6-Digit Verification Code
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- New Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"new_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div class=\"floating-label-group\">
                            <input type=\"password\"
                                   class=\"floating-input\"
                                   name=\"confirm_password\"
                                   placeholder=\" \"
                                   minlength=\"8\"
                                   required>
                            <label class=\"floating-label\">
                                <i class=\"fas fa-lock\" style=\"color: var(--ca-focus-blue); margin-right: 8px;\"></i>
                                Confirm New Password
                            </label>
                            <div class=\"input-border\"></div>
                        </div>

                        <!-- CSRF Token -->
                        <input type=\"hidden\" name=\"_token\" value=\"{{ csrf_token('reset_password_merged') }}\">

                        <!-- Change Password Button -->
                        <button type=\"submit\" class=\"login-btn\">
                            <i class=\"fas fa-key btn-icon\"></i>
                            <span>Change Password</span>
                        </button>
                    </form>

                    <!-- Form Footer -->
                    <div class=\"form-footer\">
                        <a href=\"{{ path('app_forgot_password') }}\" class=\"back-link\">
                            <i class=\"fas fa-envelope me-2\"></i>Change Email
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
", "security/reset_password_merged.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\security\\reset_password_merged.html.twig");
    }
}
