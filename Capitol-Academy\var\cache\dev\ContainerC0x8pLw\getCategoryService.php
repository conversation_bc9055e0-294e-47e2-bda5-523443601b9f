<?php

namespace ContainerC0x8pLw;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCategoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.8IESKP1.App\Entity\Category' shared service.
     *
     * @return \App\Entity\Category
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.8IESKP1": it needs an instance of "App\\Entity\\Category" but this type has been excluded in "config/services.yaml".');
    }
}
